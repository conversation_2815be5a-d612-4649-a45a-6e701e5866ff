"""Main FastAPI application factory."""

from fastapi import FastAPI, status
from fastapi.middleware.cors import CORSMiddleware

from .config import get_settings
from .events import lifespan
from api.middleware.logging import create_logging_middleware

settings = get_settings()

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: The configured FastAPI application instance.
    """
    # Initialize FastAPI application with lifespan events
    app = FastAPI(
        title=settings.APP_NAME,
        description="API for downloading YouTube subtitles and summarizing text using AI",
        version=settings.APP_VERSION,
        docs_url=settings.DOCS_URL,
        redoc_url=settings.REDOC_URL,
        openapi_url=settings.OPENAPI_URL,
        lifespan=lifespan,
    )

    # Configure CORS
    configure_cors(app)

    # Add middleware
    configure_middleware(app)

    # Configure error handlers
    configure_error_handlers(app)

    # Include API routers
    configure_routers(app)

    # Add WebSocket routes
    configure_websocket_routes(app)

    # Add health check endpoint
    add_health_check(app)

    return app

def configure_cors(app: FastAPI) -> None:
    """Configure CORS middleware."""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, replace with specific origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

def configure_middleware(app: FastAPI) -> None:
    """Configure application middleware."""
    from api.middleware.metrics import metrics_middleware
    from api.middleware.auth import auth_middleware

    # Add structured logging middleware (first to capture all requests)
    if settings.STRUCTURED_LOGGING:
        from api.middleware.logging import StructuredLoggingMiddleware
        app.add_middleware(
            StructuredLoggingMiddleware,
            exclude_paths=settings.LOG_EXCLUDE_PATHS,
            log_request_body=settings.LOG_REQUEST_BODY,
            log_response_body=settings.LOG_RESPONSE_BODY,
        )

    # Add rate limiting middleware (before auth to limit unauthenticated requests)
    if settings.RATE_LIMITING_ENABLED:
        from api.middleware.rate_limiting import (
            AdvancedRateLimitingMiddleware,
            UserLimits,
            EndpointLimits,
            RateLimitConfig
        )

        # Create custom user limits based on configuration
        user_limits = UserLimits(
            anonymous=RateLimitConfig(
                requests_per_minute=settings.ANONYMOUS_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.ANONYMOUS_RATE_LIMIT_HOUR,
                requests_per_day=settings.ANONYMOUS_RATE_LIMIT_DAY,
                burst_limit=5
            ),
            read_only=RateLimitConfig(
                requests_per_minute=settings.READ_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.READ_RATE_LIMIT_HOUR,
                requests_per_day=settings.READ_RATE_LIMIT_DAY,
                burst_limit=10
            ),
            read_write=RateLimitConfig(
                requests_per_minute=settings.WRITE_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.WRITE_RATE_LIMIT_HOUR,
                requests_per_day=settings.WRITE_RATE_LIMIT_DAY,
                burst_limit=15
            ),
            admin=RateLimitConfig(
                requests_per_minute=settings.ADMIN_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.ADMIN_RATE_LIMIT_HOUR,
                requests_per_day=settings.ADMIN_RATE_LIMIT_DAY,
                burst_limit=25
            )
        )

        # Create custom endpoint limits
        endpoint_limits = EndpointLimits()
        endpoint_limits.subtitles = RateLimitConfig(
            requests_per_minute=settings.SUBTITLES_RATE_LIMIT_MINUTE,
            requests_per_hour=settings.SUBTITLES_RATE_LIMIT_HOUR,
            requests_per_day=settings.SUBTITLES_RATE_LIMIT_DAY,
            burst_limit=3
        )
        endpoint_limits.summarize = RateLimitConfig(
            requests_per_minute=settings.SUMMARIZE_RATE_LIMIT_MINUTE,
            requests_per_hour=settings.SUMMARIZE_RATE_LIMIT_HOUR,
            requests_per_day=settings.SUMMARIZE_RATE_LIMIT_DAY,
            burst_limit=2
        )

        app.add_middleware(
            AdvancedRateLimitingMiddleware,
            user_limits=user_limits,
            endpoint_limits=endpoint_limits,
            enabled=True
        )

    # Add metrics collection middleware
    app.middleware("http")(metrics_middleware)

    # Add authentication middleware
    app.middleware("http")(auth_middleware)

def configure_routers(app: FastAPI) -> None:
    """Configure API routers."""
    # Import routers here to avoid circular imports
    from api.routers import subtitles, summarize, tasks, health, auth

    # Include API routers
    app.include_router(health.router, tags=["health"])
    app.include_router(auth.router, prefix="/api", tags=["authentication"])
    app.include_router(subtitles.router, prefix="/api", tags=["subtitles"])
    app.include_router(summarize.router, prefix="/api", tags=["summarize"])
    app.include_router(tasks.router, prefix="/api", tags=["tasks"])

def configure_websocket_routes(app: FastAPI) -> None:
    """Configure WebSocket routes."""
    # Import WebSocket routers here to avoid circular imports
    from api.websockets import subtitles_ws, summarize_ws

    # Include WebSocket routers
    app.include_router(subtitles_ws.router)
    app.include_router(summarize_ws.router)

def configure_error_handlers(app: FastAPI) -> None:
    """Configure error handlers."""
    from fastapi import HTTPException
    from fastapi.exceptions import RequestValidationError
    from api.middleware.error_handler import (
        APIError,
        api_error_handler,
        http_exception_handler,
        validation_exception_handler,
        general_exception_handler,
    )

    # Add custom error handlers
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)


def add_health_check(app: FastAPI) -> None:
    """Add health check endpoint."""
    # Health check is now handled by the health router
    pass
