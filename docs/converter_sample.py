"""
TTML to TXT converter module for yt-subs5.
"""
from pathlib import Path
from bs4 import BeautifulSoup
import aiofiles
import asyncio
from loguru import logger
from tqdm import tqdm

class TTMLConverter:
    @staticmethod
    async def convert_file(ttml_path: Path) -> Path:
        """Convert a single TTML file to TXT format.
        
        Args:
            ttml_path: Path to TTML file
            
        Returns:
            Path to the created TXT file
        """
        txt_path = ttml_path.with_suffix('.txt')
        
        # Skip if TXT already exists
        if txt_path.exists():
            logger.debug(f"Skipping {ttml_path.name} - TXT file already exists")
            return txt_path
            
        try:
            async with aiofiles.open(ttml_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                
            # Parse TTML and extract text
            soup = BeautifulSoup(content, 'xml')
            subtitles = []
            
            # Find all text elements
            for p in soup.find_all('p'):
                text = p.get_text(strip=True)
                if text:
                    subtitles.append(text)
                    
            # Write to TXT file
            async with aiofiles.open(txt_path, 'w', encoding='utf-8') as f:
                await f.write('\n'.join(subtitles))
                
            logger.success(f"Successfully converted {ttml_path.name} to TXT")
            return txt_path
            
        except Exception as e:
            logger.error(f"Error converting {ttml_path.name}: {str(e)}")
            raise
            
    @staticmethod
    async def convert_directory(directory: Path) -> list[Path]:
        """Convert all TTML files in a directory to TXT format.
        
        Args:
            directory: Directory containing TTML files
            
        Returns:
            List of paths to created TXT files
        """
        logger.info(f"Converting TTML files in {directory}")
        
        # Find all TTML files recursively
        ttml_files = list(directory.rglob('*.ttml'))
        if not ttml_files:
            logger.warning(f"No TTML files found in {directory}")
            return []
            
        converted_files = []
        
        # Create progress bar
        pbar = tqdm(total=len(ttml_files), desc="Converting files")
        
        # Process files concurrently
        tasks = []
        for ttml_file in ttml_files:
            task = asyncio.create_task(TTMLConverter.convert_file(ttml_file))
            task.add_done_callback(lambda _: pbar.update(1))
            tasks.append(task)
            
        # Wait for all conversions to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        pbar.close()
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Conversion failed: {str(result)}")
            elif isinstance(result, Path):
                converted_files.append(result)
                
        logger.info(f"Converted {len(converted_files)} files successfully")
        return converted_files
