# Логирование и разработка

## Конфигурация логирования

### Настройка уровня логирования через .env файл

Уровень логирования теперь настраивается через переменную окружения `LOG_LEVEL` в файле `.env`:

```bash
# .env
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### Доступные уровни логирования

- **DEBUG** - Детальная отладочная информация
- **INFO** - Общая информация о работе приложения (по умолчанию)
- **WARNING** - Предупреждения о потенциальных проблемах
- **ERROR** - Ошибки, которые не останавливают работу приложения
- **CRITICAL** - Критические ошибки, которые могут остановить приложение

### Структурированное логирование

Приложение поддерживает структурированное логирование с помощью `structlog` и автоматически перехватывает все стандартные логи (uvicorn, websockets):

```bash
# .env
STRUCTURED_LOGGING=true  # Использовать structlog
LOG_JSON_FORMAT=false    # JSON формат для production
LOG_REQUEST_BODY=false   # Логировать тела запросов
LOG_RESPONSE_BODY=false  # Логировать тела ответов
```

### Фильтрация шумных логов

Система автоматически фильтрует шумные логи:
- WebSocket ping/pong сообщения (keepalive)
- Uvicorn access логи в не-debug режиме
- Повторяющиеся системные сообщения

### Форматы вывода

**Обычный режим (цветной):**
```
10:30:27 | INFO     | api.endpoints.subtitles | Processing subtitle request
```

**Debug режим (с деталями):**
```
10:30:27 | DEBUG    | api.endpoints.subtitles:extract_subtitles:45 | Processing subtitle request
```

**JSON режим (для production):**
```json
{"timestamp": "2024-01-15T10:30:27.123Z", "level": "INFO", "logger": "api.endpoints.subtitles", "message": "Processing subtitle request"}
```

## Режим разработки

### Auto-reload через .env файл

Для автоматической перезагрузки при изменении кода установите в `.env`:

```bash
# .env
RELOAD=true
```

### Запуск с параметрами командной строки

Вы можете переопределить настройки из `.env` файла через командную строку:

```bash
# Включить auto-reload
python main.py --reload

# Установить уровень логирования
python main.py --log-level DEBUG

# Использовать JSON формат логов
python main.py --json-logs

# Комбинирование параметров
python main.py --reload --log-level DEBUG --json-logs
```

## Примеры конфигурации

### Разработка

```bash
# .env для разработки
LOG_LEVEL=DEBUG
RELOAD=true
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false  # Цветной вывод для удобства
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=false
```

### Production

```bash
# .env для production
LOG_LEVEL=INFO
RELOAD=false
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=true   # JSON для парсинга логов
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
```

### Отладка (максимальная детализация)

```bash
# .env для отладки
LOG_LEVEL=DEBUG
RELOAD=true
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false  # Цветной вывод с деталями
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=true
```

### Тихий режим (минимум логов)

```bash
# .env для минимального логирования
LOG_LEVEL=WARNING
RELOAD=false
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
```

## Логи в файлах

Логи автоматически сохраняются в файл `app.log` с ротацией:

- **Размер файла**: максимум 10 MB
- **Хранение**: 1 неделя
- **Формат**: человекочитаемый (не JSON)

## Миграция со старой системы

### Было (deprecated):

```bash
python main.py --debug
```

### Стало:

```bash
# Через .env файл
LOG_LEVEL=DEBUG

# Или через командную строку
python main.py --log-level DEBUG
```

## Полезные команды

```bash
# Запуск в режиме разработки
python main.py --reload --log-level DEBUG

# Запуск с JSON логами для тестирования
python main.py --json-logs --log-level INFO

# Запуск в production режиме (используются настройки из .env)
python main.py
```
