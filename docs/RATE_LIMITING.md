# Система Rate Limiting

## Обзор

В рамках Фазы 3 улучшений проекта была реализована комплексная система rate limiting, которая обеспечивает:

- **Многоуровневое ограничение** - по минутам, часам и дням
- **Пользовательские лимиты** - разные лимиты для разных типов пользователей
- **Эндпойнт-специфичные лимиты** - более строгие лимиты для ресурсоемких операций
- **Защита от burst-атак** - ограничение на короткие всплески запросов
- **Адаптивное ограничение** - автоматическое снижение лимитов при ошибках
- **Graceful degradation** - плавное снижение производительности вместо полного отказа

## Архитектура

### 1. HTTP Rate Limiting (`api/middleware/rate_limiting.py`)

Middleware для ограничения входящих HTTP запросов:

```python
from api.middleware.rate_limiting import AdvancedRateLimitingMiddleware

# Автоматически интегрируется в FastAPI
app.add_middleware(AdvancedRateLimitingMiddleware)
```

### 2. Worker Rate Limiting (`worker/rate_limiter.py`)

Система для ограничения запросов к внешним сервисам:

```python
from worker.rate_limiter import create_youtube_rate_limiter

youtube_limiter = create_youtube_rate_limiter(
    requests_per_minute=15,
    requests_per_hour=60,
    burst_limit=3
)
```

## Типы лимитов

### Пользовательские лимиты

| Тип пользователя | Мин/час/день | Burst | Описание |
|------------------|--------------|-------|----------|
| **Anonymous** | 10/100/500 | 5 | Неаутентифицированные пользователи |
| **Read-only** | 30/500/2000 | 10 | Пользователи с правами чтения |
| **Read-write** | 60/1000/5000 | 15 | Пользователи с правами записи |
| **Admin** | 120/2000/10000 | 25 | Администраторы |

### Эндпойнт-специфичные лимиты

| Эндпойнт | Лимиты | Burst | Обоснование |
|----------|--------|-------|-------------|
| `/health`, `/metrics` | 120/3600/86400 | 30 | Мониторинг должен работать |
| `/api/auth/*` | 20/100/500 | 5 | Защита от брутфорса |
| `/api/subtitles/*` | 10/100/500 | 3 | Ресурсоемкие операции |
| `/api/summarize/*` | 5/50/200 | 2 | Очень ресурсоемкие операции |
| `/api/tasks/*` | 30/300/1000 | 10 | Управление задачами |

## Конфигурация

### Переменные окружения

```bash
# Основные настройки
RATE_LIMITING_ENABLED=true

# Лимиты для анонимных пользователей
ANONYMOUS_RATE_LIMIT_MINUTE=10
ANONYMOUS_RATE_LIMIT_HOUR=100
ANONYMOUS_RATE_LIMIT_DAY=500

# Лимиты для пользователей с правами чтения
READ_RATE_LIMIT_MINUTE=30
READ_RATE_LIMIT_HOUR=500
READ_RATE_LIMIT_DAY=2000

# Лимиты для пользователей с правами записи
WRITE_RATE_LIMIT_MINUTE=60
WRITE_RATE_LIMIT_HOUR=1000
WRITE_RATE_LIMIT_DAY=5000

# Лимиты для администраторов
ADMIN_RATE_LIMIT_MINUTE=120
ADMIN_RATE_LIMIT_HOUR=2000
ADMIN_RATE_LIMIT_DAY=10000

# Эндпойнт-специфичные лимиты
SUBTITLES_RATE_LIMIT_MINUTE=10
SUBTITLES_RATE_LIMIT_HOUR=100
SUBTITLES_RATE_LIMIT_DAY=500

SUMMARIZE_RATE_LIMIT_MINUTE=5
SUMMARIZE_RATE_LIMIT_HOUR=50
SUMMARIZE_RATE_LIMIT_DAY=200
```

### Отключение rate limiting

```bash
# Для разработки
RATE_LIMITING_ENABLED=false
```

## HTTP Headers

### Информационные заголовки

Каждый ответ содержит информацию о лимитах:

```http
X-RateLimit-Limit-Minute: 60
X-RateLimit-Limit-Hour: 1000
X-RateLimit-Limit-Day: 5000
X-RateLimit-Remaining-Minute: 45
X-RateLimit-Remaining-Hour: 823
X-RateLimit-Remaining-Day: 4156
X-RateLimit-Reset: 1642678800
```

### Ответ при превышении лимита

```http
HTTP/1.1 429 Too Many Requests
Retry-After: 60
X-RateLimit-Limit-Minute: 60
X-RateLimit-Remaining-Minute: 0

{
  "error": "rate_limit_exceeded",
  "message": "Too many requests. Please slow down.",
  "details": {
    "retry_after": 60,
    "limit_per_minute": 60,
    "limit_per_hour": 1000,
    "limit_per_day": 5000,
    "requests_per_minute": 60,
    "requests_per_hour": 234,
    "requests_per_day": 1456
  }
}
```

## Внутренний Rate Limiting

### Для YouTube API

```python
from worker.rate_limiter import create_youtube_rate_limiter

youtube_limiter = create_youtube_rate_limiter(
    requests_per_minute=15,
    requests_per_hour=60,
    burst_limit=3
)

# Использование
async def download_subtitles(video_id: str):
    # Проверить и подождать если нужно
    if not await youtube_limiter.wait_if_needed(task_id=video_id):
        raise Exception("Rate limit exceeded")
    
    try:
        # Выполнить запрос
        result = await youtube_api_call(video_id)
        
        # Записать успешный запрос
        await youtube_limiter.record_request(video_id, success=True)
        
        return result
    except Exception as e:
        # Записать неудачный запрос
        await youtube_limiter.record_request(video_id, success=False)
        raise
```

### Для Gemini AI

```python
from worker.rate_limiter import create_gemini_rate_limiter

gemini_limiter = create_gemini_rate_limiter(
    requests_per_minute=5,
    requests_per_hour=30,
    burst_limit=2
)

async def summarize_text(text: str, task_id: str):
    if not await gemini_limiter.wait_if_needed(task_id):
        raise Exception("Rate limit exceeded")
    
    try:
        result = await gemini_api_call(text)
        await gemini_limiter.record_request(task_id, success=True)
        return result
    except Exception as e:
        await gemini_limiter.record_request(task_id, success=False)
        raise
```

## Адаптивное ограничение

Система автоматически адаптируется к состоянию внешних сервисов:

### При успешных запросах
- Постепенно восстанавливает нормальные лимиты
- Уменьшает множитель ограничения на 10% при каждом успехе

### При ошибках
- Увеличивает строгость ограничений в 1.5 раза
- Вводит cooldown период с экспоненциальным ростом
- Максимальный множитель ограничения: 4x

```python
# Пример адаптивного поведения
initial_limit = 15  # запросов в минуту

# После 3 ошибок подряд:
effective_limit = 15 / 4.0 = 3.75  # ~4 запроса в минуту
cooldown = 3 * (2^2) = 12 секунд  # между попытками
```

## Мониторинг

### Метрики rate limiting

```python
# Получить статистику
stats = youtube_limiter.get_stats()

{
    "service_type": "youtube",
    "total_requests": 1234,
    "total_blocked": 45,
    "total_retries": 23,
    "requests_last_minute": 12,
    "requests_last_hour": 156,
    "adaptive_multiplier": 1.2,
    "consecutive_failures": 0,
    "active_tasks": 3,
    "config": {
        "requests_per_minute": 15,
        "requests_per_hour": 60,
        "burst_limit": 3,
        "cooldown_seconds": 2,
        "max_retries": 3
    }
}
```

### Логирование

Все события rate limiting логируются с структурированными данными:

```json
{
  "timestamp": "2024-01-15T10:30:45.123456Z",
  "level": "warning",
  "event": "rate_limit_exceeded",
  "client_id": "user:123",
  "path": "/api/subtitles",
  "method": "POST",
  "user_permissions": ["read", "write"],
  "rate_info": {
    "requests_per_minute": 60,
    "limit_per_minute": 60,
    "retry_after": 60
  }
}
```

## Тестирование

### Unit тесты

```python
import pytest
from api.middleware.rate_limiting import SlidingWindowRateLimiter, RateLimitConfig

@pytest.mark.asyncio
async def test_rate_limiting():
    config = RateLimitConfig(
        requests_per_minute=5,
        requests_per_hour=20,
        requests_per_day=100,
        burst_limit=2
    )
    
    limiter = SlidingWindowRateLimiter()
    
    # Первые запросы должны проходить
    for i in range(5):
        allowed, _ = limiter.is_allowed("test_user", config)
        assert allowed
    
    # Шестой запрос должен быть заблокирован
    allowed, rate_info = limiter.is_allowed("test_user", config)
    assert not allowed
    assert rate_info["retry_after"] > 0
```

### Integration тесты

```python
from fastapi.testclient import TestClient

def test_rate_limiting_integration(client: TestClient):
    # Выполнить максимальное количество запросов
    for i in range(10):  # Лимит для анонимных пользователей
        response = client.get("/api/subtitles")
        if i < 9:
            assert response.status_code != 429
        else:
            assert response.status_code == 429
            assert "retry_after" in response.json()["details"]
```

## Лучшие практики

### 1. Graceful handling

```python
import asyncio
from fastapi import HTTPException

async def handle_rate_limited_operation():
    try:
        return await perform_operation()
    except RateLimitExceeded as e:
        # Логировать и вернуть понятную ошибку
        logger.warning("Rate limit exceeded", retry_after=e.retry_after)
        raise HTTPException(
            status_code=429,
            detail=f"Rate limit exceeded. Retry after {e.retry_after} seconds",
            headers={"Retry-After": str(e.retry_after)}
        )
```

### 2. Клиентская обработка

```javascript
// JavaScript клиент
async function apiCall(url, options = {}) {
    const response = await fetch(url, options);
    
    if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        console.log(`Rate limited. Retry after ${retryAfter} seconds`);
        
        // Автоматический retry с задержкой
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return apiCall(url, options);
    }
    
    return response;
}
```

### 3. Мониторинг и алерты

```python
# Настройка алертов
if stats["total_blocked"] / stats["total_requests"] > 0.1:
    send_alert("High rate limit rejection rate")

if stats["adaptive_multiplier"] > 2.0:
    send_alert("External service degraded performance")
```

## Производительность

### Оптимизация памяти

- Автоматическая очистка старых записей
- Sliding window с ограниченным размером
- Периодическая очистка неактивных клиентов

### Масштабирование

- In-memory хранение для одного инстанса
- Для кластера: интеграция с Redis (планируется)
- Минимальный overhead: ~1-2ms на запрос

## Troubleshooting

### Проблема: Слишком много 429 ошибок

```bash
# Проверить текущие лимиты
curl -H "Authorization: Bearer your_key" http://localhost:8000/api/subtitles \
  -I | grep X-RateLimit

# Увеличить лимиты в конфигурации
WRITE_RATE_LIMIT_MINUTE=120
```

### Проблема: Внутренние rate limiters блокируют запросы

```python
# Проверить статистику
stats = youtube_limiter.get_stats()
print(f"Adaptive multiplier: {stats['adaptive_multiplier']}")
print(f"Consecutive failures: {stats['consecutive_failures']}")

# Сбросить состояние если нужно
youtube_limiter.consecutive_failures = 0
youtube_limiter.adaptive_multiplier = 1.0
```

### Проблема: Rate limiting не работает

```python
# Проверить конфигурацию
from core.config import get_settings
settings = get_settings()
print(f"Rate limiting enabled: {settings.RATE_LIMITING_ENABLED}")

# Проверить middleware
print(app.middleware_stack)
```
