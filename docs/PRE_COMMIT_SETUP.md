# Pre-commit Setup и Конфигурация

## Обзор

В проекте настроены pre-commit хуки для автоматической проверки и форматирования кода перед коммитом. Доступны две конфигурации:

1. **`.pre-commit-config.yaml`** - основная конфигурация (рекомендуется)
2. **`.pre-commit-hatch.yaml`** - альтернативная конфигурация с использованием uv/uvx

## Установка Pre-commit

### Вариант 1: Через uv (рекомендуется)

```bash
# Установить pre-commit как инструмент
uv tool install pre-commit

# Или использовать uvx для разового запуска
uvx pre-commit --help
```

### Вариант 2: Через pip

```bash
pip install pre-commit
```

### Вариант 3: Через системный пакетный менеджер

```bash
# macOS
brew install pre-commit

# Ubuntu/Debian
sudo apt install pre-commit
```

## Настройка Pre-commit

### Основная конфигурация (.pre-commit-config.yaml)

```bash
# Установить хуки в репозиторий
pre-commit install

# Запустить хуки на всех файлах
pre-commit run --all-files

# Обновить хуки до последних версий
pre-commit autoupdate
```

### Альтернативная конфигурация (.pre-commit-hatch.yaml)

```bash
# Установить хуки с альтернативной конфигурацией
pre-commit install --config .pre-commit-hatch.yaml

# Запустить альтернативные хуки
pre-commit run --config .pre-commit-hatch.yaml --all-files
```

## Что делают хуки

### Основная конфигурация

1. **Ruff linter** - проверка и исправление ошибок кода
2. **Ruff formatter** - форматирование кода
3. **Trailing whitespace** - удаление пробелов в конце строк
4. **End of file fixer** - добавление новой строки в конце файлов
5. **YAML/TOML/JSON checker** - проверка синтаксиса конфигурационных файлов
6. **Merge conflict checker** - проверка на конфликты слияния
7. **Large files checker** - предупреждение о больших файлах
8. **UV lock checker** - проверка актуальности uv.lock

### Альтернативная конфигурация (uv/uvx)

1. **Ruff check** - через `uvx ruff check --fix`
2. **Ruff format** - через `uvx ruff format`
3. **UV sync check** - проверка зависимостей
4. **Python tests** - запуск тестов (только при pre-push)

## Использование

### Автоматический запуск

После установки хуки запускаются автоматически при каждом коммите:

```bash
git add .
git commit -m "Your commit message"
# Pre-commit хуки запустятся автоматически
```

### Ручной запуск

```bash
# Запустить все хуки
pre-commit run --all-files

# Запустить конкретный хук
pre-commit run ruff

# Пропустить хуки для коммита
git commit -m "Your message" --no-verify
```

## Конфигурация в IDE

### VS Code

Добавьте в `.vscode/settings.json`:

```json
{
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "python.formatting.provider": "none",
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.ruff": true
    }
  }
}
```

## Troubleshooting

### Проблема: "command not found: pre-commit"

```bash
# Убедитесь, что pre-commit установлен
which pre-commit

# Если не установлен, установите через uv
uv tool install pre-commit
```

### Проблема: "uvx command not found"

```bash
# Убедитесь, что uv установлен и обновлен
uv --version

# Обновите uv если нужно
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Проблема: Хуки не запускаются

```bash
# Переустановите хуки
pre-commit uninstall
pre-commit install

# Проверьте статус
pre-commit run --all-files
```

## Рекомендации

1. **Используйте основную конфигурацию** (`.pre-commit-config.yaml`) для стабильности
2. **Запускайте `pre-commit run --all-files`** после изменения конфигурации
3. **Обновляйте хуки регулярно** с помощью `pre-commit autoupdate`
4. **Настройте IDE** для использования тех же инструментов (ruff)
5. **Не пропускайте хуки** без веской причины (`--no-verify`)

## Интеграция с CI/CD

Добавьте в GitHub Actions или другую CI систему:

```yaml
- name: Run pre-commit
  run: |
    pip install pre-commit
    pre-commit run --all-files
```
