# Alternative pre-commit configuration using uv/uvx tools
# To use this config: pre-commit run --config .pre-commit-hatch.yaml
repos:
  - repo: local
    hooks:
      - id: ruff-check
        name: Ruff linter (via uvx)
        entry: uvx ruff check --fix
        language: system
        types: [python]
        require_serial: true

      - id: ruff-format
        name: Ruff formatter (via uvx)
        entry: uvx ruff format
        language: system
        types: [python]
        require_serial: true

      - id: uv-sync
        name: Check uv dependencies
        entry: uv sync --check
        language: system
        files: ^(pyproject\.toml|uv\.lock)$
        pass_filenames: false

      - id: python-tests
        name: Run Python tests
        entry: uv run python -m pytest
        language: system
        types: [python]
        pass_filenames: false
        stages: [pre-push] # Only run on pre-push, not every commit
