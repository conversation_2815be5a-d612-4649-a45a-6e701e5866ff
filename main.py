"""
Main application entry point.
This file only contains the FastAPI app initialization and includes routers.
"""

import argparse

from dotenv import load_dotenv

from core.app import create_app
from core.config import get_settings
from utils.logger import setup_logging

# Parse command line arguments
parser = argparse.ArgumentParser(
    description="YouTube Subtitles and Text Summarization API"
)
parser.add_argument(
    "--reload",
    action="store_true",
    help="Enable auto-reload for development (overrides .env setting)",
)
parser.add_argument(
    "--log-level",
    choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
    help="Override log level from .env file",
)
parser.add_argument(
    "--json-logs",
    action="store_true",
    help="Use JSON format for logs (overrides .env setting)",
)
args = parser.parse_args()

# Load environment variables
load_dotenv()

# Get settings
settings = get_settings()

# Override settings with command line arguments if provided
if args.reload:
    settings.RELOAD = True
if args.log_level:
    settings.LOG_LEVEL = args.log_level
if args.json_logs:
    settings.LOG_JSON_FORMAT = True

# Setup logging using settings from .env file (or overrides)
logger = setup_logging(
    log_level=settings.LOG_LEVEL,
    structured=settings.STRUCTURED_LOGGING,
    use_json=settings.LOG_JSON_FORMAT,
)

# Create FastAPI application
app = create_app()


if __name__ == "__main__":
    import uvicorn

    # Use settings from configuration
    uvicorn.run(
        app,
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),  # uvicorn expects lowercase
    )
