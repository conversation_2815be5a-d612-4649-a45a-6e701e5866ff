"""
TTML to TXT converter module.
"""

import asyncio
import re
from typing import Optional
from bs4 import BeautifulSoup
from loguru import logger


class TTMLConverter:
    """Converter for TTML (Timed Text Markup Language) to plain text format."""

    @staticmethod
    def convert_ttml_to_txt(ttml_content: str) -> str:
        """
        Convert TTML content to plain text format.

        Args:
            ttml_content: TTML content as string

        Returns:
            Plain text content with subtitles

        Raises:
            ValueError: If TTML content is invalid or empty
            Exception: For other parsing errors
        """
        if not ttml_content or not ttml_content.strip():
            raise ValueError("TTML content cannot be empty")

        try:
            # Parse TTML content using BeautifulSoup with XML parser
            soup = BeautifulSoup(ttml_content, "xml")

            # Extract text from all <p> elements (subtitle paragraphs)
            subtitles = []

            # Find all text elements - typically <p> tags in TTML
            for p in soup.find_all("p"):
                text = p.get_text(strip=True)
                if text:
                    # Clean up the text - remove extra whitespace and normalize
                    cleaned_text = re.sub(r"\s+", " ", text).strip()
                    if cleaned_text:
                        subtitles.append(cleaned_text)

            # If no <p> tags found, try to extract from other common TTML elements
            if not subtitles:
                # Try <span> elements
                for span in soup.find_all("span"):
                    text = span.get_text(strip=True)
                    if text:
                        cleaned_text = re.sub(r"\s+", " ", text).strip()
                        if cleaned_text:
                            subtitles.append(cleaned_text)

            # If still no content, try to get all text content
            if not subtitles:
                # Get all text content from the document
                all_text = soup.get_text(strip=True)
                if all_text:
                    # Split by common delimiters and clean up
                    lines = re.split(r"[\n\r]+", all_text)
                    for line in lines:
                        cleaned_line = re.sub(r"\s+", " ", line).strip()
                        if (
                            cleaned_line and len(cleaned_line) > 1
                        ):  # Ignore single characters
                            subtitles.append(cleaned_line)

            if not subtitles:
                logger.warning("No text content found in TTML")
                return ""

            # Join all subtitles with newlines
            result = "\n".join(subtitles)

            logger.debug(
                f"Successfully converted TTML to TXT. Found {len(subtitles)} subtitle lines."
            )
            return result

        except Exception as e:
            logger.error(f"Error parsing TTML content: {str(e)}")
            raise ValueError(f"Failed to parse TTML content: {str(e)}") from e

    @staticmethod
    async def convert_ttml_to_txt_async(ttml_content: str) -> str:
        """
        Async wrapper for TTML to TXT conversion.

        Args:
            ttml_content: TTML content as string

        Returns:
            Plain text content with subtitles
        """
        # Run the synchronous conversion in a thread pool to avoid blocking
        return await asyncio.to_thread(TTMLConverter.convert_ttml_to_txt, ttml_content)


class TTMLValidationError(ValueError):
    """Custom exception for TTML validation errors."""

    pass


class TTMLParsingError(Exception):
    """Custom exception for TTML parsing errors."""

    pass
