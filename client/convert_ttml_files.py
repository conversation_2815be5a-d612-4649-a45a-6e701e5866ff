import asyncio
import json
import sys
from pathlib import Path
import subprocess
import argparse

from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.console import Console

# Конфигурация сервера по умолчанию
DEFAULT_HOST = "localhost"
DEFAULT_PORT = 8000

# Тестовый API токен для авторизации
TEST_API_TOKEN = "ak_s8049sqAdPdJ-brzIfR5A-6cofyP2b12o58ZGyLYEiE"


class TtmlConverter:
    def __init__(
        self,
        directory: str,
        host: str = DEFAULT_HOST,
        port: int = DEFAULT_PORT,
        api_token: str = TEST_API_TOKEN,
        debug: bool = False,
    ):
        self.debug = debug
        self._log_debug(f"TtmlConverter initializing with debug mode: {self.debug}")

        self.directory = Path(directory).resolve()
        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}"
        self.api_token = api_token
        self.server_process = None
        self.console = Console()
        self._log_debug(
            f"TtmlConverter initialized. Directory: {self.directory}, Host: {self.host}, Port: {self.port}"
        )

    def _log_debug(self, message: str, exc_info: bool = False):
        if self.debug:
            # Use the logger instance defined in if __name__ == "__main__":
            logger.debug(message, exc_info=exc_info)

    async def check_server_available(self) -> bool:
        """Проверка доступности сервера через POST запрос к эндпоинту /ping"""
        self._log_debug("Checking server availability...")
        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/ping", timeout=2) as response:
                    response_json = await response.json()
                    self._log_debug(
                        f"Server check response status: {response.status}, message: {response_json}"
                    )
                    return (
                        response.status == 200
                        and response_json.get("message") == "pong"
                    )
        except Exception as e:
            self._log_debug(f"Server check failed or server not running: {e}")
            self.console.print(  # This is a user-facing message, keep as console.print
                f"[blue]Сторонний сервер не запущен. Запуск нового экземпляра FastAPI.[/blue]"
            )
            return False

    async def start_server(self):
        """Запуск FastAPI сервера"""
        self._log_debug(
            "Attempting to start local server instance (if not already running)."
        )
        # Проверяем, доступен ли уже сервер
        if await self.check_server_available():
            self.console.print(
                "[green]Сервер уже запущен и доступен[/green]"
            )  # User-facing
            self._log_debug("Server already running and available.")
            return

        try:
            # Запуск сервера в отдельном процессе
            server_script = Path(__file__).parent.parent / "main.py"
            self._log_debug(f"Starting server script: {server_script}")
            self.server_process = subprocess.Popen(
                [sys.executable, str(server_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            self._log_debug(
                f"Server process started with PID: {self.server_process.pid if self.server_process else 'N/A'}"
            )
            # Ждем немного, чтобы сервер успел запуститься
            await asyncio.sleep(3)
        except Exception as e:
            self.console.print(
                f"[red]Ошибка при запуске сервера: {e}[/red]"
            )  # User-facing
            self._log_debug(f"Error during server startup: {e}", exc_info=True)
            sys.exit(1)

    def cleanup(self):
        """Очистка ресурсов при завершении"""
        self._log_debug("Cleanup called.")
        if self.server_process:
            self._log_debug(
                f"Terminating server process with PID: {self.server_process.pid}"
            )
            self.server_process.terminate()
            self.server_process.wait()
            self._log_debug("Server process terminated.")

    async def process_file(
        self, file_path: Path, progress: Progress, task_id: int
    ) -> bool:
        """Обработка одного TTML файла"""
        self._log_debug(f"Processing file: {file_path}")
        try:
            base_name = file_path.stem
            txt_path = file_path.parent / f"{base_name}.txt"

            if txt_path.exists():
                progress.update(
                    task_id,
                    description=f"[yellow]Пропущен (TXT существует): {file_path.name}[/yellow]",
                )
                return False

            ttml_content = file_path.read_text(encoding="utf-8")
            if not ttml_content.strip():
                progress.update(
                    task_id,
                    description=f"[red]Пропущен (пустой файл): {file_path.name}[/red]",
                )
                return False

            # Отправляем запрос на конвертацию через REST API
            try:
                import aiohttp

                self._log_debug(
                    f"Sending TTML content to server for conversion: {file_path.name}"
                )
                progress.update(
                    task_id,
                    description=f"Отправка на сервер: {file_path.name}",
                )

                async with aiohttp.ClientSession() as session:
                    # Подготовка данных запроса
                    request_body_dict = {"ttml_text": ttml_content}

                    # Добавляем заголовок авторизации
                    headers = {
                        "Authorization": f"Bearer {self.api_token}",
                        "Content-Type": "application/json",
                    }

                    self._log_debug(
                        f"Sending request with auth token to {self.base_url}/api/convert/ttml-to-txt"
                    )

                    async with session.post(
                        f"{self.base_url}/api/convert/ttml-to-txt",
                        json=request_body_dict,  # Используем json= вместо data=
                        headers=headers,
                        timeout=60,
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            self._log_debug(
                                f"Server returned error for {file_path.name}: {response.status}, {error_text}"
                            )
                            progress.update(
                                task_id,
                                description=f"[red]Ошибка ({file_path.name}): Сервер вернул код {response.status}[/red]",
                            )
                            return False
                        response_data = await response.json()
                        txt_content = response_data.get("txt_content")

                        if not txt_content:
                            self._log_debug(
                                f"Server response missing txt_content for {file_path.name}"
                            )
                            progress.update(
                                task_id,
                                description=f"[red]Ошибка ({file_path.name}): Отсутствует контент в ответе[/red]",
                            )
                            return False
                        # Сохраняем очищенный текст в TXT файл
                        txt_path.write_text(txt_content, encoding="utf-8")

                        # Копируем временные метки из исходного файла
                        import os

                        source_stat = os.stat(file_path)
                        os.utime(
                            txt_path,
                            (
                                source_stat.st_atime,
                                source_stat.st_mtime,
                            ),
                        )

                        self._log_debug(
                            f"Conversion successful for {file_path.name}, saved to {txt_path}"
                        )
                        progress.update(
                            task_id,
                            description=f"[green]Обработан: {file_path.name}[/green]",
                        )
                        return True
            except Exception as e:
                self._log_debug(
                    f"Error during API request for {file_path.name}: {e}",
                    exc_info=True,
                )
                progress.update(
                    task_id,
                    description=f"[red]Ошибка ({file_path.name}): {str(e)}[/red]",
                )
                return False
        except Exception as e:
            self._log_debug(
                f"Unhandled error in process_file for {file_path.name}: {e}",
                exc_info=True,
            )
            progress.update(
                task_id, description=f"[red]Ошибка ({file_path.name}): {str(e)}[/red]"
            )
            return False

    async def process_directory(self):
        """Обработка всех TTML файлов в директории"""
        self._log_debug(f"Scanning directory: {self.directory} for .ttml files.")
        if not self.directory.exists() or not self.directory.is_dir():
            self.console.print(
                f"[red]Ошибка: Директория {self.directory} не существует[/red]"
            )
            self._log_debug(
                f"Directory {self.directory} does not exist or is not a directory."
            )
            return

        ttml_files = list(self.directory.glob("**/*.ttml"))
        self._log_debug(f"Found {len(ttml_files)} .ttml files to process.")
        if not ttml_files:
            self.console.print("[yellow]Предупреждение: TTML файлы не найдены[/yellow]")
            return

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            # Добавляем общий прогресс
            main_task = progress.add_task(
                "[blue]Обработка файлов...[/blue]", total=len(ttml_files)
            )

            # Обрабатываем каждый файл
            completed = 0
            for ttml_file in ttml_files:
                file_task = progress.add_task(
                    f"Подготовка: {ttml_file.name}", total=None
                )
                if await self.process_file(ttml_file, progress, file_task):
                    completed += 1
                progress.update(main_task, advance=1)
                progress.remove_task(file_task)

            # Выводим итоговую статистику
            self.console.print(f"\n[green]Обработка завершена![/green]")
            self.console.print(f"Всего файлов: {len(ttml_files)}")
            self.console.print(f"Успешно обработано: {completed}")
            self.console.print(f"Пропущено/Ошибок: {len(ttml_files) - completed}")


async def main():
    # Настраиваем парсер аргументов командной строки
    parser = argparse.ArgumentParser(description="Конвертация TTML файлов в TXT")
    parser.add_argument("directory", help="Директория с TTML файлами")
    parser.add_argument(
        "--host",
        default=DEFAULT_HOST,
        help=f"Хост сервера (по умолчанию: {DEFAULT_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Порт сервера (по умолчанию: {DEFAULT_PORT})",
    )
    parser.add_argument(
        "--api-token",
        default=TEST_API_TOKEN,
        help="API токен для авторизации запросов к серверу",
    )
    parser.add_argument(  # Added --debug argument
        "--debug",
        action="store_true",
        help="Enable detailed debug logging on the client side.",
    )

    args = parser.parse_args()

    # Configure logging level based on --debug flag
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logger.debug("Debug mode enabled by command line flag.")

    try:
        # Запускаем сервер, передавая debug_mode
        converter = TtmlConverter(
            args.directory,
            args.host,
            args.port,
            api_token=args.api_token,
            debug=args.debug,
        )
        await converter.start_server()

        # Обрабатываем файлы
        await converter.process_directory()

    except KeyboardInterrupt:
        logger.info("\nПрерывание работы...")  # Use logger
    finally:
        if "converter" in locals() and hasattr(converter, "cleanup"):
            converter.cleanup()


if __name__ == "__main__":
    import logging

    logger = logging.getLogger(__name__)
    # BasicConfig is set in main() after parsing args now.
    asyncio.run(main())
