import asyncio
import json
import sys
import uuid
from pathlib import Path
import websockets
import subprocess
import argparse
import mdformat

from modes_client import get_available_modes, is_valid_mode

from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.console import Console

# from rich.live import Live
# from rich.panel import Panel
# from rich.text import Text

# Конфигурация сервера по умолчанию
DEFAULT_HOST = "localhost"
DEFAULT_PORT = 8000


class FileSummarizer:
    def __init__(
        self,
        directory: str,
        mode: str = "default",
        host: str = DEFAULT_HOST,
        port: int = DEFAULT_PORT,
        format_markdown: bool = False,
        debug: bool = False,  # Added debug flag
    ):
        self.debug = debug  # Store debug flag
        self._log_debug(
            f"FileSummarizer initializing with debug mode: {self.debug}")

        if not is_valid_mode(mode):
            raise ValueError(
                f"Режим должен быть одним из: {', '.join(get_available_modes())}"
            )
        self.directory = Path(directory).resolve()
        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}"
        self.ws_url = f"ws://{self.host}:{self.port}/ws/summarize"
        self.server_process = None
        self.console = Console()
        self.mode: str = mode
        self.reconnect_attempts = 3
        self.reconnect_delay = 5
        # self.ping_interval = 30 # Removed as client won't send pings
        self.connection_timeout = 60  # Timeout for ws.recv()
        self.format_markdown = format_markdown
        # DEPRECATED: client_uid is no longer used by the server but kept for backward compatibility
        self.client_uid = None  # No longer generate client_uid
        self._log_debug(
            f"FileSummarizer initialized. Directory: {self.directory}, Mode: {self.mode}, Host: {self.host}, Port: {self.port}"
        )

    def _log_debug(self, message: str, exc_info: bool = False):
        if self.debug:
            # Use the logger instance defined in if __name__ == "__main__":
            logger.debug(message, exc_info=exc_info)

    async def check_server_available(self) -> bool:
        """Проверка доступности сервера через POST запрос к эндпоинту /ping"""
        self._log_debug("Checking server availability...")
        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/ping", timeout=2) as response:
                    response_json = await response.json()
                    self._log_debug(
                        f"Server check response status: {response.status}, message: {response_json}"
                    )
                    return (
                        response.status == 200
                        and response_json.get("message") == "pong"
                    )
        except Exception as e:
            self._log_debug(f"Server check failed or server not running: {e}")
            self.console.print(  # This is a user-facing message, keep as console.print
                f"[blue]Сторонний сервер не запущен. Запуск нового экземпляра FastAPI.[/blue]"
            )
            return False

    async def start_server(self):
        """Запуск FastAPI сервера"""
        self._log_debug(
            "Attempting to start local server instance (if not already running)."
        )
        # Проверяем, доступен ли уже сервер
        if await self.check_server_available():
            self.console.print(
                "[green]Сервер уже запущен и доступен[/green]"
            )  # User-facing
            self._log_debug("Server already running and available.")
            return

        try:
            # Запуск сервера в отдельном процессе
            server_script = Path(__file__).parent.parent / "main.py"
            self._log_debug(f"Starting server script: {server_script}")
            self.server_process = subprocess.Popen(
                [sys.executable, str(server_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            self._log_debug(
                f"Server process started with PID: {self.server_process.pid if self.server_process else 'N/A'}"
            )
            # Ждем немного, чтобы сервер успел запуститься
            await asyncio.sleep(3)
        except Exception as e:
            self.console.print(
                f"[red]Ошибка при запуске сервера: {e}[/red]"
            )  # User-facing
            self._log_debug(f"Error during server startup: {e}", exc_info=True)
            sys.exit(1)

    def cleanup(self):
        """Очистка ресурсов при завершении"""
        self._log_debug("Cleanup called.")
        if self.server_process:
            self._log_debug(
                f"Terminating server process with PID: {self.server_process.pid}"
            )
            self.server_process.terminate()
            self.server_process.wait()
            self._log_debug("Server process terminated.")

    async def process_file(
        self, file_path: Path, progress: Progress, task_id: int
    ) -> bool:
        """Обработка одного файла"""
        self._log_debug(f"Processing file: {file_path}")
        try:
            base_name = file_path.stem
            md_path = (
                file_path.parent / f"{base_name}-{self.mode}.md"
                if self.mode != "default"
                else file_path.parent / f"{base_name}.md"
            )
            if md_path.exists():
                progress.update(
                    task_id,
                    description=f"[yellow]Пропущен (MD существует): {file_path.name}[/yellow]",
                )
                return False

            text = file_path.read_text(encoding="utf-8")
            if not text.strip():
                progress.update(
                    task_id,
                    description=f"[red]Пропущен (пустой файл): {file_path.name}[/red]",
                )
                return False

            for attempt in range(self.reconnect_attempts):
                self._log_debug(
                    f"Attempting WebSocket connection to {self.ws_url} (Attempt {attempt + 1}/{self.reconnect_attempts}) for file {file_path.name}"
                )
                try:
                    async with websockets.connect(
                        self.ws_url,
                        close_timeout=10,
                    ) as ws:
                        self._log_debug(
                            f"WebSocket connected to {self.ws_url} for file {file_path.name}"
                        )
                        current_task_id_from_server = None

                        initial_request_data = {
                            "type": "text_input",  # This matches MessageType.TEXT.value
                            "content": text,
                            "mode": self.mode,
                            # client_uid is deprecated and no longer sent
                        }
                        initial_request_json = json.dumps(initial_request_data)
                        self._log_debug(
                            f"Sending task request to server for {file_path.name}: {initial_request_json}"
                        )
                        await ws.send(initial_request_json)

                        try:
                            while True:
                                try:
                                    response = await asyncio.wait_for(
                                        ws.recv(), timeout=self.connection_timeout
                                    )
                                    self._log_debug(
                                        f"Received raw message from server for {file_path.name}: {response}"
                                    )
                                    data = json.loads(response)

                                    if data.get("type") == "ping" and "task_id" in data:
                                        server_task_id_ping = data["task_id"]
                                        self._log_debug(
                                            f"Received PING from server for task_id: {server_task_id_ping} (file: {file_path.name})"
                                        )
                                        pong_message = {
                                            "type": "pong",
                                            "task_id": server_task_id_ping,
                                        }
                                        pong_message_json = json.dumps(
                                            pong_message)
                                        self._log_debug(
                                            f"Sending PONG to server for {file_path.name}: {pong_message_json}"
                                        )
                                        await ws.send(pong_message_json)
                                        continue

                                    server_task_id = data.get("task_id")
                                    status = data.get("status")
                                    self._log_debug(
                                        f"Received status update for {file_path.name}: {status}, Task ID from server: {server_task_id}"
                                    )

                                    if server_task_id and server_task_id != "error":
                                        if (
                                            current_task_id_from_server
                                            != server_task_id
                                        ):
                                            current_task_id_from_server = server_task_id
                                            # logger.info(f"Associated with server task_id: {current_task_id_from_server}") # This is an info log
                                            self._log_debug(
                                                f"Client now associated with server task_id: {current_task_id_from_server} for file {file_path.name}"
                                            )

                                    if status == "completed" and data.get("summary"):
                                        if self.format_markdown:
                                            # Форматируем Markdown текст с помощью mdformat
                                            formatted_text = mdformat.text(
                                                data["summary"],
                                                options={
                                                    "wrap": "no",
                                                    "number": True,
                                                    "markdown_flavor": "gfm",
                                                    # "ordered_list_enumeration": "keep"
                                                },
                                            )
                                            md_path.write_text(
                                                formatted_text, encoding="utf-8"
                                            )
                                        else:
                                            # Сохраняем текст без форматирования
                                            md_path.write_text(
                                                data["summary"], encoding="utf-8"
                                            )
                                        # Копируем временные метки из исходного файла
                                        import os

                                        source_stat = os.stat(file_path)
                                        os.utime(
                                            md_path,
                                            (
                                                source_stat.st_atime,
                                                source_stat.st_mtime,
                                            ),
                                        )
                                        self._log_debug(
                                            f"Summary received for {file_path.name}, saving to {md_path}"
                                        )
                                        if self.format_markdown:
                                            formatted_text = mdformat.text(
                                                data["summary"],
                                                options={
                                                    "wrap": "no",
                                                    "number": True,
                                                    "markdown_flavor": "gfm",
                                                },
                                            )
                                            md_path.write_text(
                                                formatted_text, encoding="utf-8"
                                            )
                                        else:
                                            md_path.write_text(
                                                data["summary"], encoding="utf-8"
                                            )

                                        import os

                                        source_stat = os.stat(file_path)
                                        os.utime(
                                            md_path,
                                            (
                                                source_stat.st_atime,
                                                source_stat.st_mtime,
                                            ),
                                        )
                                        self._log_debug(
                                            f"Summary successfully saved to {md_path} for file {file_path.name}"
                                        )

                                        progress.update(
                                            task_id,
                                            description=f"[green]Обработан ({self.mode}): {file_path.name}[/green]",
                                        )
                                        return True
                                    elif status == "failed":
                                        error = data.get(
                                            "error", "Неизвестная ошибка")
                                        self._log_debug(
                                            f"Task failed for {file_path.name}. Error: {error}"
                                        )
                                        progress.update(
                                            task_id,
                                            description=f"[red]Ошибка ({file_path.name}): {error}[/red]",
                                        )
                                        return False
                                    else:
                                        progress.update(
                                            task_id,
                                            description=f"Обработка: {file_path.name}",
                                        )
                                except asyncio.TimeoutError:
                                    self._log_debug(
                                        f"Timeout waiting for server message for {file_path.name}. Connection timeout: {self.connection_timeout}s. Server should have sent ping within this if alive."
                                    )
                                    # This implies server might be unresponsive or network issue if no ping received.
                                    # The main loop will retry connection if this leads to ConnectionError.
                                    # For now, just continue to wait for next message or actual connection error.
                                    continue
                        except Exception as e:
                            self._log_debug(
                                f"Exception during WebSocket communication for {file_path.name}: {e}",
                                exc_info=True,
                            )
                            raise e  # Re-raise to be caught by outer try-except for reconnect logic

                except (websockets.exceptions.ConnectionClosed, ConnectionError) as e:
                    self._log_debug(
                        f"Connection error for {file_path.name} (Attempt {attempt + 1}): {e}",
                        exc_info=True
                    )
                    if attempt < self.reconnect_attempts - 1:
                        wait_time = self.reconnect_delay * (attempt + 1)
                        self._log_debug(
                            f"Connection error for {file_path.name}. Retrying in {wait_time}s. Attempt {attempt + 1}/{self.reconnect_attempts}"
                        )
                        progress.update(
                            task_id,
                            description=f"[yellow]Переподключение ({attempt + 1}/{self.reconnect_attempts}): {file_path.name}[/yellow]",
                        )
                        await asyncio.sleep(wait_time)
                        continue
                    self._log_debug(
                        f"Failed to connect after {self.reconnect_attempts} attempts for {file_path.name}."
                    )
                    progress.update(
                        task_id,
                        description=f"[red]Ошибка: Сервер недоступен после {self.reconnect_attempts} попыток подключения[/red]"
                    )
                    # Завершаем работу клиента при потере связи с сервером
                    self.cleanup()
                    sys.exit(1)
        except Exception as e:
            self._log_debug(
                f"Unhandled error in process_file for {file_path.name}: {e}",
                exc_info=True,
            )
            progress.update(
                task_id, description=f"[red]Ошибка ({file_path.name}): {str(e)}[/red]"
            )
            return False

    async def process_directory(self):
        """Обработка всех файлов в директории"""
        self._log_debug(
            f"Scanning directory: {self.directory} for .txt files.")
        if not self.directory.exists() or not self.directory.is_dir():
            self.console.print(
                f"[red]Ошибка: Директория {self.directory} не существует[/red]"
            )
            self._log_debug(
                f"Directory {self.directory} does not exist or is not a directory."
            )
            return

        txt_files = list(self.directory.glob("**/*.txt"))
        self._log_debug(f"Found {len(txt_files)} .txt files to process.")
        if not txt_files:
            self.console.print(
                "[yellow]Предупреждение: TXT файлы не найдены[/yellow]")
            return

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            # Добавляем общий прогресс
            main_task = progress.add_task(
                "[blue]Обработка файлов...[/blue]", total=len(txt_files)
            )

            # Обрабатываем каждый файл
            completed = 0
            for txt_file in txt_files:
                file_task = progress.add_task(
                    f"Подготовка: {txt_file.name}", total=None
                )
                if await self.process_file(txt_file, progress, file_task):
                    completed += 1
                progress.update(main_task, advance=1)
                progress.remove_task(file_task)

            # Выводим итоговую статистику
            self.console.print(f"\n[green]Обработка завершена![/green]")
            self.console.print(f"Всего файлов: {len(txt_files)}")
            self.console.print(f"Успешно обработано: {completed}")
            self.console.print(
                f"Пропущено/Ошибок: {len(txt_files) - completed}")


async def main():
    # Настраиваем парсер аргументов командной строки
    parser = argparse.ArgumentParser(
        description="Суммаризация текстовых файлов")
    parser.add_argument("directory", help="Директория с текстовыми файлами")
    parser.add_argument(
        "--mode",
        choices=get_available_modes(),
        default="default",
        help=f"Режим суммаризации ({', '.join(get_available_modes())})",
    )
    parser.add_argument(
        "--host",
        default=DEFAULT_HOST,
        help=f"Хост сервера (по умолчанию: {DEFAULT_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Порт сервера (по умолчанию: {DEFAULT_PORT})",
    )
    parser.add_argument(
        "--format",
        action="store_true",
        help="Форматировать Markdown текст с помощью mdformat",
    )
    parser.add_argument(
        "--totaltest",
        action="store_true",
        help="Запустить обработку каждого файла по всем доступным режимам",
    )
    parser.add_argument(  # Added --debug argument
        "--debug",
        action="store_true",
        help="Enable detailed debug logging on the client side.",
    )

    args = parser.parse_args()

    # Configure logging level based on --debug flag
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logger.debug("Debug mode enabled by command line flag.")

    try:
        # Запускаем сервер, передавая debug_mode
        summarizer = FileSummarizer(
            args.directory,
            args.mode,
            args.host,
            args.port,
            args.format,
            debug=args.debug,
        )
        await summarizer.start_server()

        # Обрабатываем файлы
        if args.totaltest:
            # Обрабатываем каждый файл по всем доступным режимам
            self_cleanup = summarizer.cleanup
            summarizer.cleanup = lambda: None  # Временно отключаем cleanup

            from modes_client import AVAILABLE_MODES

            console = Console()
            console.print(
                f"[blue]Запуск тестирования по всем режимам ({len(AVAILABLE_MODES)} режимов)[/blue]"
            )

            total_modes = len(AVAILABLE_MODES)
            completed_modes = 0

            for mode in AVAILABLE_MODES:
                console.print(
                    f"[blue]Обработка файлов в режиме: {mode} ({completed_modes + 1}/{total_modes})[/blue]"
                )
                mode_summarizer = FileSummarizer(
                    args.directory,
                    mode,
                    args.host,
                    args.port,
                    args.format,
                    debug=args.debug,  # Pass debug flag
                )
                await mode_summarizer.process_directory()
                completed_modes += 1

            console.print(
                f"\n[green]Тестирование по всем режимам завершено![/green]")
            console.print(
                f"Всего обработано режимов: {completed_modes}/{total_modes}")

            if "self_cleanup" in locals():
                summarizer.cleanup = self_cleanup
        else:
            await summarizer.process_directory()

    except KeyboardInterrupt:
        logger.info("\nПрерывание работы...")  # Use logger
    finally:
        if "summarizer" in locals() and hasattr(summarizer, "cleanup"):
            summarizer.cleanup()


if __name__ == "__main__":
    import logging  # Moved import to top for consistency, but effective here

    logger = logging.getLogger(__name__)
    # BasicConfig is set in main() after parsing args now.
    asyncio.run(main())
