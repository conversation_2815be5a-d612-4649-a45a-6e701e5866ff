"""
Advanced rate limiting middleware for FastAPI.

This module provides comprehensive rate limiting capabilities including:
- Per-user rate limiting based on API keys
- Per-IP rate limiting for unauthenticated requests
- Different limits for different endpoints
- Sliding window rate limiting
- Integration with authentication system
"""

import time
from collections import defaultdict, deque
from collections.abc import Callable
from dataclasses import dataclass, field
from typing import Any

import structlog
from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware

from core.config import get_settings

settings = get_settings()
logger = structlog.get_logger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting rules."""

    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_limit: int = 10  # Maximum burst requests
    window_size: int = 60  # Window size in seconds


@dataclass
class UserLimits:
    """Rate limits for different user types."""

    anonymous: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            requests_per_day=500,
            burst_limit=5,
        )
    )

    read_only: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=30,
            requests_per_hour=500,
            requests_per_day=2000,
            burst_limit=10,
        )
    )

    read_write: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=60,
            requests_per_hour=1000,
            requests_per_day=5000,
            burst_limit=15,
        )
    )

    admin: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=120,
            requests_per_hour=2000,
            requests_per_day=10000,
            burst_limit=25,
        )
    )


@dataclass
class EndpointLimits:
    """Rate limits for different endpoints."""

    # Health and metrics endpoints (very permissive)
    health: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=120,
            requests_per_hour=3600,
            requests_per_day=86400,
            burst_limit=30,
        )
    )

    # Authentication endpoints
    auth: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=20,
            requests_per_hour=100,
            requests_per_day=500,
            burst_limit=5,
        )
    )

    # Subtitle extraction (resource intensive)
    subtitles: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            requests_per_day=500,
            burst_limit=3,
        )
    )

    # Text summarization (very resource intensive)
    summarize: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=5,
            requests_per_hour=50,
            requests_per_day=200,
            burst_limit=2,
        )
    )

    # Task management
    tasks: RateLimitConfig = field(
        default_factory=lambda: RateLimitConfig(
            requests_per_minute=30,
            requests_per_hour=300,
            requests_per_day=1000,
            burst_limit=10,
        )
    )


class SlidingWindowRateLimiter:
    """
    Sliding window rate limiter implementation.

    Uses a sliding window approach to track requests over time periods.
    More accurate than fixed window but uses more memory.
    """

    def __init__(self):
        # Store request timestamps for each identifier
        self.requests: dict[str, deque] = defaultdict(lambda: deque())
        # Store last cleanup time to avoid excessive cleanup
        self.last_cleanup: dict[str, float] = defaultdict(float)

    def is_allowed(
        self, identifier: str, config: RateLimitConfig
    ) -> tuple[bool, dict[str, Any]]:
        """
        Check if a request is allowed based on rate limiting rules.

        Args:
            identifier: Unique identifier for the client (IP, user ID, etc.)
            config: Rate limiting configuration

        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        current_time = time.time()

        # Clean up old requests periodically
        self._cleanup_old_requests(identifier, current_time)

        requests = self.requests[identifier]

        # Check different time windows
        minute_ago = current_time - 60
        hour_ago = current_time - 3600
        day_ago = current_time - 86400

        # Count requests in each window
        requests_last_minute = sum(1 for req_time in requests if req_time > minute_ago)
        requests_last_hour = sum(1 for req_time in requests if req_time > hour_ago)
        requests_last_day = sum(1 for req_time in requests if req_time > day_ago)

        # Check burst limit (last 10 seconds)
        burst_window = current_time - 10
        requests_burst = sum(1 for req_time in requests if req_time > burst_window)

        # Rate limit info for headers
        rate_limit_info = {
            "requests_per_minute": requests_last_minute,
            "requests_per_hour": requests_last_hour,
            "requests_per_day": requests_last_day,
            "requests_burst": requests_burst,
            "limit_per_minute": config.requests_per_minute,
            "limit_per_hour": config.requests_per_hour,
            "limit_per_day": config.requests_per_day,
            "burst_limit": config.burst_limit,
            "reset_time": int(current_time + 60),  # Next minute reset
        }

        # Check if any limit is exceeded
        if (
            requests_last_minute >= config.requests_per_minute
            or requests_last_hour >= config.requests_per_hour
            or requests_last_day >= config.requests_per_day
            or requests_burst >= config.burst_limit
        ):
            # Calculate retry after time
            if requests_burst >= config.burst_limit:
                rate_limit_info["retry_after"] = 10  # Wait 10 seconds for burst
            elif requests_last_minute >= config.requests_per_minute:
                rate_limit_info["retry_after"] = 60  # Wait 1 minute
            elif requests_last_hour >= config.requests_per_hour:
                rate_limit_info["retry_after"] = 3600  # Wait 1 hour
            else:
                rate_limit_info["retry_after"] = 86400  # Wait 1 day

            return False, rate_limit_info

        # Record the request
        requests.append(current_time)

        return True, rate_limit_info

    def _cleanup_old_requests(self, identifier: str, current_time: float):
        """Clean up old request timestamps to save memory."""
        # Only cleanup every 5 minutes to avoid excessive processing
        if current_time - self.last_cleanup[identifier] < 300:
            return

        self.last_cleanup[identifier] = current_time
        requests = self.requests[identifier]

        # Remove requests older than 1 day
        day_ago = current_time - 86400
        while requests and requests[0] < day_ago:
            requests.popleft()

        # If no recent requests, remove the identifier entirely
        if not requests:
            del self.requests[identifier]
            del self.last_cleanup[identifier]


class AdvancedRateLimitingMiddleware(BaseHTTPMiddleware):
    """
    Advanced rate limiting middleware with per-user and per-endpoint limits.
    """

    def __init__(
        self,
        app,
        user_limits: UserLimits | None = None,
        endpoint_limits: EndpointLimits | None = None,
        enabled: bool = True,
    ):
        super().__init__(app)
        self.enabled = enabled
        self.user_limits = user_limits or UserLimits()
        self.endpoint_limits = endpoint_limits or EndpointLimits()
        self.limiter = SlidingWindowRateLimiter()

        # Paths to exclude from rate limiting
        self.excluded_paths = {"/docs", "/redoc", "/openapi.json", "/favicon.ico"}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with rate limiting."""
        if not self.enabled:
            return await call_next(request)

        # Skip rate limiting for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)

        # Get client identifier and user info
        client_id = self._get_client_identifier(request)
        user_permissions = self._get_user_permissions(request)

        # Determine rate limit configuration
        config = self._get_rate_limit_config(request.url.path, user_permissions)

        # Check rate limit
        is_allowed, rate_info = self.limiter.is_allowed(client_id, config)

        if not is_allowed:
            # Log rate limit violation
            logger.warning(
                "Rate limit exceeded",
                client_id=client_id,
                path=request.url.path,
                method=request.method,
                user_permissions=user_permissions,
                rate_info=rate_info,
            )

            # Create rate limit exceeded response
            response = self._create_rate_limit_response(rate_info)
            return response

        # Process the request
        response = await call_next(request)

        # Add rate limit headers
        self._add_rate_limit_headers(response, rate_info)

        # Log successful request with rate limit info
        logger.debug(
            "Request processed with rate limiting",
            client_id=client_id,
            path=request.url.path,
            method=request.method,
            rate_info=rate_info,
        )

        return response

    def _get_client_identifier(self, request: Request) -> str:
        """Get unique identifier for the client."""
        # Try to get user ID from request state (set by auth middleware)
        user = getattr(request.state, "user", {})
        if user and user.get("id"):
            return f"user:{user['id']}"

        # Fall back to IP address
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"

    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"

    def _get_user_permissions(self, request: Request) -> list:
        """Get user permissions from request state."""
        user = getattr(request.state, "user", {})
        return user.get("permissions", [])

    def _get_rate_limit_config(self, path: str, permissions: list) -> RateLimitConfig:
        """Get rate limit configuration based on path and user permissions."""
        # Determine endpoint type
        endpoint_config = self._get_endpoint_config(path)

        # Determine user type config
        user_config = self._get_user_config(permissions)

        # Use the more restrictive limits
        return RateLimitConfig(
            requests_per_minute=min(
                endpoint_config.requests_per_minute, user_config.requests_per_minute
            ),
            requests_per_hour=min(
                endpoint_config.requests_per_hour, user_config.requests_per_hour
            ),
            requests_per_day=min(
                endpoint_config.requests_per_day, user_config.requests_per_day
            ),
            burst_limit=min(endpoint_config.burst_limit, user_config.burst_limit),
        )

    def _get_endpoint_config(self, path: str) -> RateLimitConfig:
        """Get rate limit config based on endpoint path."""
        if path.startswith("/health") or path.startswith("/metrics"):
            return self.endpoint_limits.health
        elif path.startswith("/api/auth"):
            return self.endpoint_limits.auth
        elif path.startswith("/api/subtitles"):
            return self.endpoint_limits.subtitles
        elif path.startswith("/api/summarize"):
            return self.endpoint_limits.summarize
        elif path.startswith("/api/tasks"):
            return self.endpoint_limits.tasks
        else:
            # Default to auth limits for unknown endpoints
            return self.endpoint_limits.auth

    def _get_user_config(self, permissions: list) -> RateLimitConfig:
        """Get rate limit config based on user permissions."""
        if not permissions:
            return self.user_limits.anonymous
        elif "admin" in permissions:
            return self.user_limits.admin
        elif "write" in permissions:
            return self.user_limits.read_write
        elif "read" in permissions:
            return self.user_limits.read_only
        else:
            return self.user_limits.anonymous

    def _create_rate_limit_response(self, rate_info: dict[str, Any]) -> Response:
        """Create HTTP 429 response for rate limit exceeded."""
        from fastapi.responses import JSONResponse

        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "rate_limit_exceeded",
                "message": "Too many requests. Please slow down.",
                "details": {
                    "retry_after": rate_info.get("retry_after", 60),
                    "limit_per_minute": rate_info["limit_per_minute"],
                    "limit_per_hour": rate_info["limit_per_hour"],
                    "limit_per_day": rate_info["limit_per_day"],
                    "requests_per_minute": rate_info["requests_per_minute"],
                    "requests_per_hour": rate_info["requests_per_hour"],
                    "requests_per_day": rate_info["requests_per_day"],
                },
            },
            headers={
                "Retry-After": str(rate_info.get("retry_after", 60)),
                "X-RateLimit-Limit-Minute": str(rate_info["limit_per_minute"]),
                "X-RateLimit-Limit-Hour": str(rate_info["limit_per_hour"]),
                "X-RateLimit-Limit-Day": str(rate_info["limit_per_day"]),
                "X-RateLimit-Remaining-Minute": str(
                    max(
                        0,
                        rate_info["limit_per_minute"]
                        - rate_info["requests_per_minute"],
                    )
                ),
                "X-RateLimit-Remaining-Hour": str(
                    max(0, rate_info["limit_per_hour"] - rate_info["requests_per_hour"])
                ),
                "X-RateLimit-Remaining-Day": str(
                    max(0, rate_info["limit_per_day"] - rate_info["requests_per_day"])
                ),
                "X-RateLimit-Reset": str(rate_info["reset_time"]),
            },
        )

    def _add_rate_limit_headers(self, response: Response, rate_info: dict[str, Any]):
        """Add rate limit headers to successful responses."""
        response.headers["X-RateLimit-Limit-Minute"] = str(
            rate_info["limit_per_minute"]
        )
        response.headers["X-RateLimit-Limit-Hour"] = str(rate_info["limit_per_hour"])
        response.headers["X-RateLimit-Limit-Day"] = str(rate_info["limit_per_day"])
        response.headers["X-RateLimit-Remaining-Minute"] = str(
            max(0, rate_info["limit_per_minute"] - rate_info["requests_per_minute"])
        )
        response.headers["X-RateLimit-Remaining-Hour"] = str(
            max(0, rate_info["limit_per_hour"] - rate_info["requests_per_hour"])
        )
        response.headers["X-RateLimit-Remaining-Day"] = str(
            max(0, rate_info["limit_per_day"] - rate_info["requests_per_day"])
        )
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset_time"])


# Factory function for easy integration
def create_rate_limiting_middleware(
    enabled: bool = True,
    user_limits: UserLimits | None = None,
    endpoint_limits: EndpointLimits | None = None,
) -> AdvancedRateLimitingMiddleware:
    """
    Create rate limiting middleware with default or custom configuration.

    Args:
        enabled: Whether rate limiting is enabled
        user_limits: Custom user limits configuration
        endpoint_limits: Custom endpoint limits configuration

    Returns:
        Configured rate limiting middleware
    """
    return AdvancedRateLimitingMiddleware(
        app=None,  # Will be set by FastAPI
        user_limits=user_limits,
        endpoint_limits=endpoint_limits,
        enabled=enabled,
    )
