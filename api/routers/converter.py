"""TTML to TXT conversion endpoints."""

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from loguru import logger

from models.schemas import (
    ConvertTTMLRequest,
    ConvertTTMLResponse,
    TaskStatus,
)
from api.middleware.error_handler import ValidationError
from api.middleware.auth import require_write
from services.converter_service import ConverterService
from typing import Dict, Any, Optional

router = APIRouter()

# Флаг для отключения требования авторизации для эндпоинта /convert/ttml-to-txt
# Установите в True, чтобы отключить требование авторизации; False, чтобы включить
DISABLE_AUTH_FOR_TTML_CONVERSION = True


@router.post("/convert/ttml-to-txt", response_model=ConvertTTMLResponse)
async def convert_ttml_to_txt(
    request: ConvertTTMLRequest, 
    user: Optional[Dict[str, Any]] = Depends(require_write) if not DISABLE_AUTH_FOR_TTML_CONVERSION else None
):
    """
    Convert TTML content to TXT format.

    This endpoint performs synchronous conversion of TTML (Timed Text Markup Language)
    content to plain text format. The conversion is fast and doesn't require queuing.

    Requires: write permission
    """
    logger.debug("Received /convert/ttml-to-txt request")

    try:
        # Create converter service
        converter_service = ConverterService()

        logger.debug("Starting TTML to TXT conversion")

        # Perform the conversion
        response = await converter_service.convert_ttml_to_txt(request)

        logger.debug(f"TTML conversion completed. Status: {response.status}")

        # Return appropriate HTTP status code based on conversion result
        if response.status == TaskStatus.COMPLETED:
            status_code = 200
        else:
            status_code = 400  # Bad request for conversion failures

        return JSONResponse(status_code=status_code, content=response.model_dump())

    except ValueError as e:
        # Invalid input data
        logger.warning(f"Validation error in TTML conversion: {e}")
        raise ValidationError(str(e))

    except Exception as e:
        # Unexpected errors
        logger.error(
            f"Unexpected error in TTML conversion endpoint: {str(e)}", exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content=ConvertTTMLResponse(
                status=TaskStatus.FAILED, error="Internal server error"
            ).model_dump(),
        )
