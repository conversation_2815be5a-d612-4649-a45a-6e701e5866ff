#!/usr/bin/env python3
"""
Test script for the TTML to TXT conversion API endpoint.
"""
import asyncio
import aiohttp
import json

# Test configuration
BASE_URL = "http://localhost:8000"
API_TOKEN = "admin_key_67890"  # Use the default admin key

# Test TTML content
TEST_TTML = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
      <p>Hello, this is the first subtitle.</p>
      <p>This is the second subtitle line.</p>
      <p>And this is the third subtitle.</p>
    </div>
  </body>
</tt>"""

async def test_converter_endpoint():
    """Test the converter endpoint."""
    print("Testing TTML to TXT converter API endpoint...")

    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }

    request_data = {
        "ttml_text": TEST_TTML
    }

    try:
        async with aiohttp.ClientSession() as session:
            print(f"Sending request to {BASE_URL}/api/convert/ttml-to-txt")
            print(f"Request data: {json.dumps(request_data, indent=2)[:200]}...")

            async with session.post(
                f"{BASE_URL}/api/convert/ttml-to-txt",
                json=request_data,
                headers=headers,
                timeout=30
            ) as response:
                print(f"Response status: {response.status}")
                print(f"Response headers: {dict(response.headers)}")

                if response.status == 200:
                    response_data = await response.json()
                    print("✅ Success!")
                    print(f"Status: {response_data.get('status')}")
                    print(f"TXT Content: {response_data.get('txt_content')}")
                    print(f"Error: {response_data.get('error')}")
                else:
                    error_text = await response.text()
                    print(f"❌ Error {response.status}")
                    print(f"Error response: {error_text}")

    except Exception as e:
        print(f"❌ Exception: {e}")

async def test_ping():
    """Test the ping endpoint."""
    print("Testing ping endpoint...")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/ping", timeout=5) as response:
                print(f"Ping status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"Ping response: {data}")
                    return True
                else:
                    print(f"Ping failed: {response.status}")
                    return False
    except Exception as e:
        print(f"Ping exception: {e}")
        return False

async def main():
    """Main test function."""
    print("=== API Converter Test ===\n")

    # Test ping first
    if await test_ping():
        print("\n" + "="*50 + "\n")
        # Test converter
        await test_converter_endpoint()
    else:
        print("❌ Server is not available")

if __name__ == "__main__":
    asyncio.run(main())
