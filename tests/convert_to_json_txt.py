#!/usr/bin/env python3
import json
import os
import sys
from pathlib import Path

def convert_file_to_json_txt(file_path):
    """
    Convert file content to a single-line JSON string in a new .json.txt file.
    
    Args:
        file_path (str): Path to the input file
        
    Returns:
        str: Path to the created JSON file or None if error occurred
    """
    try:
        # Convert to Path object for easier path manipulation
        input_path = Path(file_path)
        
        # Create output path with .json.txt extension
        output_path = input_path.parent / f"{input_path.stem}.json.txt"
        
        # Read input file content
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create a dictionary with the content
        data = {"content": content}
        
        # Write JSON to output file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False)
        
        return str(output_path)
    
    except Exception as e:
        print(f"Error processing file: {e}", file=sys.stderr)
        return None

def main():
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <input_file>", file=sys.stderr)
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    if not os.path.isfile(input_file):
        print(f"Error: File '{input_file}' does not exist", file=sys.stderr)
        sys.exit(1)
    
    result = convert_file_to_json_txt(input_file)
    if result:
        print(f"Successfully created: {result}")
    else:
        print("Failed to process file", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
