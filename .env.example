# Server Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=false  # Set to true for development auto-reload

# Proxy Configuration (optional)
# Format: socks5://host:port
SOCKS5_PROXY=

# yt-dlp Cookies File (optional)
# Path to a Netscape-format cookies file
# COOKIES_FILE=/path/to/your/cookies.txt
COOKIES_FILE=
# Gemini API Configuration
GEM_API=your_gemini_api_key_here

DATABASE_TYPE=sqlite
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=yt_subs

# Logging Configuration
# LOG_LEVEL: DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false  # Set to true for production JSON logs
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false

# Rate Limiting Configuration
RATE_LIMITING_ENABLED=true

# Anonymous user limits (per minute/hour/day)
ANONYMOUS_RATE_LIMIT_MINUTE=10
ANONYMOUS_RATE_LIMIT_HOUR=100
ANONYMOUS_RATE_LIMIT_DAY=500

# Read-only user limits
READ_RATE_LIMIT_MINUTE=30
READ_RATE_LIMIT_HOUR=500
READ_RATE_LIMIT_DAY=2000

# Read-write user limits
WRITE_RATE_LIMIT_MINUTE=60
WRITE_RATE_LIMIT_HOUR=1000
WRITE_RATE_LIMIT_DAY=5000

# Admin user limits
ADMIN_RATE_LIMIT_MINUTE=120
ADMIN_RATE_LIMIT_HOUR=2000
ADMIN_RATE_LIMIT_DAY=10000

# Endpoint-specific limits
SUBTITLES_RATE_LIMIT_MINUTE=10
SUBTITLES_RATE_LIMIT_HOUR=100
SUBTITLES_RATE_LIMIT_DAY=500

SUMMARIZE_RATE_LIMIT_MINUTE=5
SUMMARIZE_RATE_LIMIT_HOUR=50
SUMMARIZE_RATE_LIMIT_DAY=200