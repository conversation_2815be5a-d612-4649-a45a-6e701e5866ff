import sys
import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from loguru import logger as loguru_logger

# Intercept handler for standard logging
class <PERSON>ceptHandler(logging.Handler):
    """Handler to intercept standard logging and redirect to loguru"""

    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = loguru_logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        loguru_logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_structured_logging(log_level: str = "INFO", use_json: bool = False) -> structlog.BoundLogger:
    """
    Setup structured logging with both loguru and structlog.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        use_json: Use JSON format for structured logs

    Returns:
        Configured structlog logger
    """
    # Ensure log level is uppercase
    log_level = log_level.upper()
    debug = log_level == "DEBUG"

    # Configure structlog processors
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
    ]

    if debug:
        # Add more detailed processors for debug mode
        processors.extend([
            structlog.processors.CallsiteParameterAdder(
                parameters=[structlog.processors.CallsiteParameter.FILENAME,
                           structlog.processors.CallsiteParameter.FUNC_NAME,
                           structlog.processors.CallsiteParameter.LINENO]
            ),
        ])

    if use_json:
        # JSON format for production/structured logging
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable format for development
        processors.append(structlog.dev.ConsoleRenderer(colors=True))

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level)
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Setup loguru for backward compatibility and standard logging interception
    setup_loguru_logging(log_level=log_level, use_json=use_json)

    return structlog.get_logger()


def setup_loguru_logging(log_level: str = "INFO", use_json: bool = False):
    """
    Setup loguru logging for backward compatibility.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        use_json: Use JSON format for structured output
    """
    # Remove default handler
    loguru_logger.remove()

    # Ensure log level is uppercase
    log_level = log_level.upper()
    debug = log_level == "DEBUG"

    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Configure specific loggers that might be noisy
    for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "websockets", "websockets.server", "websockets.protocol"]:
        logging.getLogger(logger_name).handlers = [InterceptHandler()]
        logging.getLogger(logger_name).propagate = False

    # Add file handler with structured format
    log_file = Path(__file__).parent.parent / "app.log"
    loguru_logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        backtrace=True,
        diagnose=True,
        serialize=False,  # Keep human-readable format for file logs
    )

    # Add stdout handler with enhanced formatting
    if debug:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    else:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan> | "
            "<level>{message}</level>"
        )

    # Add JSON format option for structured output
    if use_json:
        format_string = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message} | {extra}"

    # Filter function to reduce noise from websockets ping/pong
    def filter_noisy_logs(record):
        message = record["message"].lower()
        # Filter out websockets ping/pong messages
        if any(keyword in message for keyword in ["keepalive ping", "keepalive pong", "sending ping", "received pong"]):
            return False
        # Filter out uvicorn access logs in non-debug mode
        if not debug and record["name"] == "uvicorn.access":
            return False
        return True

    loguru_logger.add(
        sys.stdout,
        colorize=not use_json,
        level=log_level,
        format=format_string,
        filter=filter_noisy_logs,
        serialize=use_json
    )


def setup_logging(log_level: str = "INFO", structured: bool = True, use_json: bool = False) -> Any:
    """
    Setup logging system.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        structured: Use structured logging (structlog) if True, otherwise use loguru
        use_json: Use JSON format for structured logs

    Returns:
        Configured logger (structlog or loguru)
    """
    if structured:
        return setup_structured_logging(log_level=log_level, use_json=use_json)
    else:
        setup_loguru_logging(log_level=log_level, use_json=use_json)
        return loguru_logger


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (optional)

    Returns:
        Structured logger instance
    """
    if name:
        return structlog.get_logger(name)
    return structlog.get_logger()
