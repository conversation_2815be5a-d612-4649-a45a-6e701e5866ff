repos:
  # Pre-commit hooks using remote repositories (recommended)
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.4
    hooks:
      - id: ruff
        name: Ruff linter
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format
        name: Ruff formatter

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        name: Trim trailing whitespace
      - id: end-of-file-fixer
        name: Fix end of files
      - id: check-yaml
        name: Check YAML syntax
      - id: check-toml
        name: Check TOML syntax
      - id: check-json
        name: Check JSON syntax
      - id: check-merge-conflict
        name: Check for merge conflicts
      - id: check-added-large-files
        name: Check for large files
        args: ["--maxkb=1000"]

  # Optional: Type checking with mypy
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.8.0
  #   hooks:
  #     - id: mypy
  #       name: Type checking with mypy
  #       additional_dependencies: [types-all]

  # Local hooks using uv (alternative approach)
  - repo: local
    hooks:
      - id: uv-lock-check
        name: Check uv.lock is up to date
        entry: uv lock --check
        language: system
        files: ^(pyproject\.toml|uv\.lock)$
        pass_filenames: false
