#!/usr/bin/env python3
"""
Simple test script for TTML to TXT converter.
"""
import asyncio
from worker.converters.ttml_converter import TTMLConverter

# Sample TTML content for testing
SAMPLE_TTML = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml" xmlns:tts="http://www.w3.org/ns/ttml#styling">
  <head>
    <styling>
      <style xml:id="s1" tts:textAlign="center" tts:color="white"/>
    </styling>
  </head>
  <body>
    <div>
      <p begin="00:00:01.000" end="00:00:03.000">Hello, this is the first subtitle.</p>
      <p begin="00:00:04.000" end="00:00:06.000">This is the second subtitle line.</p>
      <p begin="00:00:07.000" end="00:00:10.000">And this is the third subtitle with some longer text.</p>
    </div>
  </body>
</tt>"""

SIMPLE_TTML = """<tt>
<body>
<div>
<p>First line of subtitles</p>
<p>Second line of subtitles</p>
<p>Third line of subtitles</p>
</div>
</body>
</tt>"""

async def test_converter():
    """Test the TTML converter."""
    print("Testing TTML to TXT converter...")
    
    converter = TTMLConverter()
    
    # Test 1: Complex TTML
    print("\n=== Test 1: Complex TTML ===")
    try:
        result1 = await converter.convert_ttml_to_txt_async(SAMPLE_TTML)
        print("✅ Conversion successful!")
        print("Result:")
        print(result1)
        print(f"Lines: {len(result1.splitlines())}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Simple TTML
    print("\n=== Test 2: Simple TTML ===")
    try:
        result2 = await converter.convert_ttml_to_txt_async(SIMPLE_TTML)
        print("✅ Conversion successful!")
        print("Result:")
        print(result2)
        print(f"Lines: {len(result2.splitlines())}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Invalid TTML
    print("\n=== Test 3: Invalid TTML ===")
    try:
        result3 = await converter.convert_ttml_to_txt_async("This is not TTML")
        print("✅ Conversion successful!")
        print("Result:")
        print(result3)
    except Exception as e:
        print(f"❌ Expected error: {e}")
    
    # Test 4: Empty content
    print("\n=== Test 4: Empty content ===")
    try:
        result4 = await converter.convert_ttml_to_txt_async("")
        print("✅ Conversion successful!")
        print("Result:")
        print(result4)
    except Exception as e:
        print(f"❌ Expected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_converter())
